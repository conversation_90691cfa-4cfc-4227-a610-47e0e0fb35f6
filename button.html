<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Animated Social Media Icons</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            min-height: 100vh;
            background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 25%, #6c5ce7 50%, #a29bfe 75%, #8e44ad 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-family: 'Arial', sans-serif;
            perspective: 1000px;
            transition: all 0.5s ease;
            position: relative;
            overflow: hidden;
        }

        /* Animated background particles */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 118, 117, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 40% 40%, rgba(162, 155, 254, 0.3) 0%, transparent 50%);
            animation: float 6s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* Dark mode styles */
        body.dark-mode {
            background: linear-gradient(135deg, #2d1b69 0%, #11998e 25%, #38ef7d 50%, #0f3460 75%, #2d1b69 100%);
        }

        body.dark-mode::before {
            background: radial-gradient(circle at 20% 80%, rgba(45, 27, 105, 0.4) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(17, 153, 142, 0.4) 0%, transparent 50%),
                        radial-gradient(circle at 40% 40%, rgba(56, 239, 125, 0.4) 0%, transparent 50%);
        }

        /* Light mode styles */
        body.light-mode {
            background: linear-gradient(135deg, #e8cbc0 0%, #636fa4 25%, #e8cbc0 50%, #f093fb 75%, #f5576c 100%);
        }

        body.light-mode::before {
            background: radial-gradient(circle at 20% 80%, rgba(232, 203, 192, 0.5) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(99, 111, 164, 0.5) 0%, transparent 50%),
                        radial-gradient(circle at 40% 40%, rgba(240, 147, 251, 0.5) 0%, transparent 50%);
        }

        /* Theme toggle button */
        .theme-toggle {
            position: absolute;
            top: 30px;
            right: 30px;
            width: 70px;
            height: 35px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50px;
            cursor: pointer;
            display: flex;
            align-items: center;
            padding: 3px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .theme-toggle:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }

        .theme-toggle-slider {
            width: 29px;
            height: 29px;
            background: white;
            border-radius: 50%;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            position: relative;
        }

        .theme-toggle-slider i {
            font-size: 14px;
            color: #ffd700;
            transition: all 0.3s ease;
        }

        /* Dark mode toggle styles */
        body.dark-mode .theme-toggle {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            border-color: rgba(255, 255, 255, 0.1);
        }

        body.dark-mode .theme-toggle-slider {
            transform: translateX(35px);
            background: #2c3e50;
        }

        body.dark-mode .theme-toggle-slider i {
            color: #74b9ff;
        }

        /* Light mode toggle styles */
        body.light-mode .theme-toggle {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            border-color: rgba(0, 0, 0, 0.1);
        }

        body.light-mode .theme-toggle-slider {
            transform: translateX(0px);
            background: #fff;
        }

        body.light-mode .theme-toggle-slider i {
            color: #ffd700;
        }

        .social-container {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
        }

        .social-icon {
            position: relative;
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform-style: preserve-3d;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                0 4px 16px rgba(0, 0, 0, 0.2),
                inset 0 2px 4px rgba(255, 255, 255, 0.2),
                inset 0 -2px 4px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .social-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease;
        }

        .social-icon:hover::before {
            left: 100%;
        }

        .social-icon i {
            font-size: 35px;
            color: white;
            transition: all 0.3s ease;
            z-index: 2;
            text-shadow:
                0 2px 4px rgba(0, 0, 0, 0.5),
                0 4px 8px rgba(0, 0, 0, 0.3),
                0 8px 16px rgba(0, 0, 0, 0.2),
                0 0 10px rgba(255, 255, 255, 0.1);
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        .social-icon:hover {
            transform: translateY(-15px) rotateX(15deg) rotateY(5deg) scale(1.15);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.4),
                0 15px 30px rgba(0, 0, 0, 0.3),
                0 5px 15px rgba(0, 0, 0, 0.2),
                inset 0 2px 6px rgba(255, 255, 255, 0.3),
                inset 0 -2px 6px rgba(0, 0, 0, 0.3),
                0 0 20px rgba(255, 255, 255, 0.1);
        }

        .social-icon:hover i {
            transform: scale(1.25) rotateY(360deg);
            text-shadow:
                0 4px 8px rgba(0, 0, 0, 0.6),
                0 8px 16px rgba(0, 0, 0, 0.4),
                0 16px 32px rgba(0, 0, 0, 0.3),
                0 0 20px rgba(255, 255, 255, 0.2);
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.4));
        }

        /* Facebook */
        .facebook {
            background: linear-gradient(135deg, #3b5998, #8b9dc3);
            border: 3px solid #3b5998;
        }

        .facebook:hover {
            background: linear-gradient(135deg, #8b9dc3, #3b5998);
            border-color: #8b9dc3;
        }

        /* Twitter */
        .twitter {
            background: linear-gradient(135deg, #1da1f2, #0d8bd9);
            border: 3px solid #1da1f2;
        }

        .twitter:hover {
            background: linear-gradient(135deg, #0d8bd9, #1da1f2);
            border-color: #0d8bd9;
        }

        /* Instagram */
        .instagram {
            background: linear-gradient(135deg, #e4405f, #f77737, #fccc63);
            border: 3px solid #e4405f;
        }

        .instagram:hover {
            background: linear-gradient(135deg, #fccc63, #f77737, #e4405f);
            border-color: #fccc63;
        }

        /* LinkedIn */
        .linkedin {
            background: linear-gradient(135deg, #0077b5, #00a0dc);
            border: 3px solid #0077b5;
        }

        .linkedin:hover {
            background: linear-gradient(135deg, #00a0dc, #0077b5);
            border-color: #00a0dc;
        }

        /* YouTube */
        .youtube {
            background: linear-gradient(135deg, #ff0000, #cc0000);
            border: 3px solid #ff0000;
        }

        .youtube:hover {
            background: linear-gradient(135deg, #cc0000, #ff0000);
            border-color: #cc0000;
        }

        /* GitHub */
        .github {
            background: linear-gradient(135deg, #333, #666);
            border: 3px solid #333;
        }

        .github:hover {
            background: linear-gradient(135deg, #666, #333);
            border-color: #666;
        }

        /* WhatsApp */
        .whatsapp {
            background: linear-gradient(135deg, #25d366, #128c7e);
            border: 3px solid #25d366;
        }

        .whatsapp:hover {
            background: linear-gradient(135deg, #128c7e, #25d366);
            border-color: #128c7e;
        }

        /* TikTok */
        .tiktok {
            background: linear-gradient(135deg, #ff0050, #000);
            border: 3px solid #ff0050;
        }

        .tiktok:hover {
            background: linear-gradient(135deg, #000, #ff0050);
            border-color: #000;
        }

        /* Sliding animation on container */
        .social-container {
            animation: slideIn 1s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Individual icon slide-in animation */
        .social-icon {
            animation: iconSlideIn 0.8s ease-out forwards;
            opacity: 0;
        }

        .social-icon:nth-child(1) { animation-delay: 0.1s; }
        .social-icon:nth-child(2) { animation-delay: 0.2s; }
        .social-icon:nth-child(3) { animation-delay: 0.3s; }
        .social-icon:nth-child(4) { animation-delay: 0.4s; }
        .social-icon:nth-child(5) { animation-delay: 0.5s; }
        .social-icon:nth-child(6) { animation-delay: 0.6s; }
        .social-icon:nth-child(7) { animation-delay: 0.7s; }
        .social-icon:nth-child(8) { animation-delay: 0.8s; }

        @keyframes iconSlideIn {
            from {
                opacity: 0;
                transform: translateX(-100px) rotateY(-90deg);
            }
            to {
                opacity: 1;
                transform: translateX(0) rotateY(0deg);
            }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .social-icon {
                width: 60px;
                height: 60px;
            }

            .social-icon i {
                font-size: 25px;
            }

            .social-container {
                gap: 20px;
                padding: 20px;
            }

            .theme-toggle {
                top: 20px;
                right: 20px;
                width: 60px;
                height: 30px;
            }

            .theme-toggle-slider {
                width: 24px;
                height: 24px;
            }

            .theme-toggle-slider i {
                font-size: 12px;
            }

            body.dark-mode .theme-toggle-slider {
                transform: translateX(30px);
            }
        }

        /* Additional 3D effect */
        .social-icon::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: inherit;
            border-radius: inherit;
            transform: translateZ(-10px);
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .social-icon:hover::after {
            transform: translateZ(-20px);
            opacity: 0.5;
        }

        /* Light mode social icon adjustments */
        body.light-mode .social-icon {
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.15),
                0 4px 16px rgba(0, 0, 0, 0.1),
                inset 0 2px 4px rgba(255, 255, 255, 0.3),
                inset 0 -2px 4px rgba(0, 0, 0, 0.1);
        }

        body.light-mode .social-icon:hover {
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.25),
                0 15px 30px rgba(0, 0, 0, 0.2),
                0 5px 15px rgba(0, 0, 0, 0.15),
                inset 0 2px 6px rgba(255, 255, 255, 0.4),
                inset 0 -2px 6px rgba(0, 0, 0, 0.2),
                0 0 30px rgba(255, 255, 255, 0.2);
        }

        body.light-mode .social-icon i {
            text-shadow:
                0 2px 4px rgba(0, 0, 0, 0.3),
                0 4px 8px rgba(0, 0, 0, 0.2),
                0 0 10px rgba(255, 255, 255, 0.1);
        }

        /* Dark mode social icon adjustments */
        body.dark-mode .social-icon {
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.6),
                0 4px 16px rgba(0, 0, 0, 0.4),
                inset 0 2px 4px rgba(255, 255, 255, 0.1),
                inset 0 -2px 4px rgba(0, 0, 0, 0.3);
            border-width: 2px;
        }

        body.dark-mode .social-icon:hover {
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.7),
                0 15px 30px rgba(0, 0, 0, 0.5),
                0 5px 15px rgba(0, 0, 0, 0.3),
                inset 0 2px 6px rgba(255, 255, 255, 0.2),
                inset 0 -2px 6px rgba(0, 0, 0, 0.4),
                0 0 40px rgba(255, 255, 255, 0.1);
        }

        body.dark-mode .social-icon i {
            text-shadow:
                0 2px 4px rgba(0, 0, 0, 0.7),
                0 4px 8px rgba(0, 0, 0, 0.5),
                0 8px 16px rgba(0, 0, 0, 0.3),
                0 0 15px rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body class="light-mode">
    <!-- Theme Toggle Button -->
    <div class="theme-toggle" onclick="toggleTheme()">
        <div class="theme-toggle-slider">
            <i class="fas fa-sun"></i>
        </div>
    </div>

    <div class="social-container">
        <div class="social-icon facebook">
            <i class="fab fa-facebook-f"></i>
        </div>
        <div class="social-icon twitter">
            <i class="fab fa-twitter"></i>
        </div>
        <div class="social-icon instagram">
            <i class="fab fa-instagram"></i>
        </div>
        <div class="social-icon linkedin">
            <i class="fab fa-linkedin-in"></i>
        </div>
        <div class="social-icon youtube">
            <i class="fab fa-youtube"></i>
        </div>
        <div class="social-icon github">
            <i class="fab fa-github"></i>
        </div>
        <div class="social-icon whatsapp">
            <i class="fab fa-whatsapp"></i>
        </div>
        <div class="social-icon tiktok">
            <i class="fab fa-tiktok"></i>
        </div>
    </div>

    <script>
        function toggleTheme() {
            const body = document.body;
            const toggleSlider = document.querySelector('.theme-toggle-slider i');

            if (body.classList.contains('light-mode')) {
                // Switch to dark mode
                body.classList.remove('light-mode');
                body.classList.add('dark-mode');
                toggleSlider.className = 'fas fa-moon';
                localStorage.setItem('theme', 'dark');
            } else {
                // Switch to light mode
                body.classList.remove('dark-mode');
                body.classList.add('light-mode');
                toggleSlider.className = 'fas fa-sun';
                localStorage.setItem('theme', 'light');
            }
        }

        // Load saved theme on page load
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme');
            const body = document.body;
            const toggleSlider = document.querySelector('.theme-toggle-slider i');

            if (savedTheme === 'dark') {
                body.classList.remove('light-mode');
                body.classList.add('dark-mode');
                toggleSlider.className = 'fas fa-moon';
            } else {
                body.classList.remove('dark-mode');
                body.classList.add('light-mode');
                toggleSlider.className = 'fas fa-sun';
            }
        });

        // Add keyboard accessibility
        document.querySelector('.theme-toggle').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                toggleTheme();
            }
        });

        // Make theme toggle focusable
        document.querySelector('.theme-toggle').setAttribute('tabindex', '0');
    </script>
</body>
</html>