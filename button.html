<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Animated Social Media Icons</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Arial', sans-serif;
            perspective: 1000px;
        }

        .social-container {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
        }

        .social-icon {
            position: relative;
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform-style: preserve-3d;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .social-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease;
        }

        .social-icon:hover::before {
            left: 100%;
        }

        .social-icon i {
            font-size: 35px;
            color: white;
            transition: all 0.3s ease;
            z-index: 2;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .social-icon:hover {
            transform: translateY(-10px) rotateX(15deg) rotateY(5deg) scale(1.1);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
        }

        .social-icon:hover i {
            transform: scale(1.2) rotateY(360deg);
        }

        /* Facebook */
        .facebook {
            background: linear-gradient(135deg, #3b5998, #8b9dc3);
            border: 3px solid #3b5998;
        }

        .facebook:hover {
            background: linear-gradient(135deg, #8b9dc3, #3b5998);
            border-color: #8b9dc3;
        }

        /* Twitter */
        .twitter {
            background: linear-gradient(135deg, #1da1f2, #0d8bd9);
            border: 3px solid #1da1f2;
        }

        .twitter:hover {
            background: linear-gradient(135deg, #0d8bd9, #1da1f2);
            border-color: #0d8bd9;
        }

        /* Instagram */
        .instagram {
            background: linear-gradient(135deg, #e4405f, #f77737, #fccc63);
            border: 3px solid #e4405f;
        }

        .instagram:hover {
            background: linear-gradient(135deg, #fccc63, #f77737, #e4405f);
            border-color: #fccc63;
        }

        /* LinkedIn */
        .linkedin {
            background: linear-gradient(135deg, #0077b5, #00a0dc);
            border: 3px solid #0077b5;
        }

        .linkedin:hover {
            background: linear-gradient(135deg, #00a0dc, #0077b5);
            border-color: #00a0dc;
        }

        /* YouTube */
        .youtube {
            background: linear-gradient(135deg, #ff0000, #cc0000);
            border: 3px solid #ff0000;
        }

        .youtube:hover {
            background: linear-gradient(135deg, #cc0000, #ff0000);
            border-color: #cc0000;
        }

        /* GitHub */
        .github {
            background: linear-gradient(135deg, #333, #666);
            border: 3px solid #333;
        }

        .github:hover {
            background: linear-gradient(135deg, #666, #333);
            border-color: #666;
        }

        /* WhatsApp */
        .whatsapp {
            background: linear-gradient(135deg, #25d366, #128c7e);
            border: 3px solid #25d366;
        }

        .whatsapp:hover {
            background: linear-gradient(135deg, #128c7e, #25d366);
            border-color: #128c7e;
        }

        /* TikTok */
        .tiktok {
            background: linear-gradient(135deg, #ff0050, #000);
            border: 3px solid #ff0050;
        }

        .tiktok:hover {
            background: linear-gradient(135deg, #000, #ff0050);
            border-color: #000;
        }

        /* Sliding animation on container */
        .social-container {
            animation: slideIn 1s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Individual icon slide-in animation */
        .social-icon {
            animation: iconSlideIn 0.8s ease-out forwards;
            opacity: 0;
        }

        .social-icon:nth-child(1) { animation-delay: 0.1s; }
        .social-icon:nth-child(2) { animation-delay: 0.2s; }
        .social-icon:nth-child(3) { animation-delay: 0.3s; }
        .social-icon:nth-child(4) { animation-delay: 0.4s; }
        .social-icon:nth-child(5) { animation-delay: 0.5s; }
        .social-icon:nth-child(6) { animation-delay: 0.6s; }
        .social-icon:nth-child(7) { animation-delay: 0.7s; }
        .social-icon:nth-child(8) { animation-delay: 0.8s; }

        @keyframes iconSlideIn {
            from {
                opacity: 0;
                transform: translateX(-100px) rotateY(-90deg);
            }
            to {
                opacity: 1;
                transform: translateX(0) rotateY(0deg);
            }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .social-icon {
                width: 60px;
                height: 60px;
            }

            .social-icon i {
                font-size: 25px;
            }

            .social-container {
                gap: 20px;
                padding: 20px;
            }
        }

        /* Additional 3D effect */
        .social-icon::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: inherit;
            border-radius: inherit;
            transform: translateZ(-10px);
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .social-icon:hover::after {
            transform: translateZ(-20px);
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="social-container">
        <div class="social-icon facebook">
            <i class="fab fa-facebook-f"></i>
        </div>
        <div class="social-icon twitter">
            <i class="fab fa-twitter"></i>
        </div>
        <div class="social-icon instagram">
            <i class="fab fa-instagram"></i>
        </div>
        <div class="social-icon linkedin">
            <i class="fab fa-linkedin-in"></i>
        </div>
        <div class="social-icon youtube">
            <i class="fab fa-youtube"></i>
        </div>
        <div class="social-icon github">
            <i class="fab fa-github"></i>
        </div>
        <div class="social-icon whatsapp">
            <i class="fab fa-whatsapp"></i>
        </div>
        <div class="social-icon tiktok">
            <i class="fab fa-tiktok"></i>
        </div>
    </div>
</body>
</html>